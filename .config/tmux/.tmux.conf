#~/.tmux.conf
unbind r
bind-key r source-file ~/.config/tmux/.tmux.conf

set -g prefix C-s
unbind C-b
bind-key C-s send-prefix

# Options to make tmux more pleasant
set -g mouse on
set -g default-terminal "tmux-256color"

bind-key h select-pane -L
bind-key j select-pane -D
bind-key k select-pane -U
bind-key l select-pane -R
bind-key m resize-pane -Z
unbind %
bind-key | split-window -h 
unbind '"'
bind-key - split-window -v

# Configure the catppuccin plugin
set -g @catppuccin_flavor "mocha" # latte, frappe, macchiato, or mocha
set -g @catppuccin_window_status_style "rounded" # basic, rounded, slanted, custom, or none

# Load catppuccin
run ~/.config/tmux/plugins/catppuccin/tmux/catppuccin.tmux
# For TPM, instead use `run ~/.config/tmux/plugins/tmux/catppuccin.tmux`

# Make the status line pretty and add some modules
set-option -g status-bg colour235
set-option -g status-fg colour179
# set-option -g status-attr default
set -g status-right-length 100
set -g status-left-length 100
set -g status-left ""
set -g status-right "#{E:@catppuccin_status_application}"
set -agF status-right "#{E:@catppuccin_status_cpu}"
set -ag status-right "#{E:@catppuccin_status_session}"
set -ag status-right "#{E:@catppuccin_status_uptime}"
set -agF status-right "#{E:@catppuccin_status_battery}"
set-option -g status-position top


# List of plugins
set -g @plugin 'tmux-plugins/tpm'
set -g @plugin 'tmux-plugins/tmux-sensible'
set -g @plugin 'tmux-plugins/tmux-cpu'
set -g @plugin 'tmux-plugins/tmux-battery'
set -g @plugin 'christoomey/vim-tmux-navigator'
set -g @plugin 'tmux-plugins/tmux-resurrect'
set -g @plugin 'tmux-plugins/tmux-continuum'
set -g @plugin 'b0o/tmux-autoreload'

set -g @resurrect-dir '~/Developers/Tinkering/.dotfiles/.config/tmux/sessions/'
set -g @resurrect-capture-pane-contents 'on'
set -g @resurrect-strategy-nvim 'session'
set -g @continuum-restore 'on'
set -g @continuum-boot 'on'

# Other examples:
# set -g @plugin 'github_username/plugin_name'
# set -g @plugin 'github_username/plugin_name#branch'
# set -g @plugin '**************:user/plugin'
# set -g @plugin '*****************:user/plugin'

# Initialize TMUX plugin manager (keep this line at the very bottom of tmux.conf)
run '~/.config/tmux/plugins/tpm/tpm'
# run ~/.config/tmux/plugins/tmux-resurrect/resurrect.tmux
# run ~/.config/tmux/plugins/tmux-continuum/continuum.tmux
# run ~/.config/tmux/plugins/tmux-sensible/sensible.tmux

# run ~/.config/tmux/plugins/tmux-plugins/tmux-cpu/cpu.tmux
# run ~/.config/tmux/plugins/tmux-plugins/tmux-battery/battery.tmux
# Or, if using TPM, just run TPM

set -g @resurrect-save 'm'
set -g @resurrect-restore 'n'
