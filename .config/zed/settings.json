// Zed settings
//
// For information on how to configure <PERSON><PERSON>, see the Zed
// documentation: https://zed.dev/docs/configuring-zed
//
// To see all of <PERSON>ed's default settings without changing your
// custom settings, run the `zed: Open Default Settings` command
// from the command palette
{
  "ssh_connections": [
    {
      "host": "monitoring",
      "projects": [
        {
          "paths": [
            "/etc/prometheus"
          ]
        },
        {
          "paths": [
            "/home/<USER>"
          ]
        },
        {
          "paths": [
            "/mnt/external"
          ]
        }
      ]
    },
    {
      "host": "clickstack",
      "projects": [
        {
          "paths": [
            "/home/<USER>"
          ]
        },
        {
          "paths": [
            "/mnt/ebs/clickstack"
          ]
        }
      ]
    },
    {
      "host": "clickhouse",
      "projects": []
    }
  ],
  "icon_theme": "Charmed Icons",
  "agent": {
    "default_profile": "write",
    "always_allow_tool_actions": true,
    "default_model": {
      "provider": "zed.dev",
      "model": "claude-sonnet-4"
    },
    "version": "2"
  },
  "features": {
    "edit_prediction_provider": "none"
  },
  "vim_mode": true,
  "telemetry": {
    "diagnostics": false,
    "metrics": false
  },
  "ui_font_size": 16,
  "buffer_font_size": 15.0,
  "theme": {
    "mode": "system",
    "light": "One Light",
    "dark": "Catppuccin Mocha"
  },
  "remove_trailing_whitespace_on_save": false,
  "terminal": {
    "font_family": "JetBrains Mono",
    "font_features": {
      "calt": true,
      "cv01": 13
    }
  },
  "ui_font_family": "JetBrains Mono",
  "ui_font_features": {
    "calt": true,
    "cv01": 13
  },
  "buffer_font_family": "JetBrains Mono",
  "buffer_font_features": {
    "calt": true,
    "cv01": 13
  },
  "experimental.theme_overrides": {
    "background.appearance": "transparent",
    "background": "#1e1e2eDF",
    "panel.background": "#00000000",
    "editor.background": "#00000000",
    "tab_bar.background": "#00000000",
    "terminal.background": "#00000000",
    "toolbar.background": "#00000000",
    "tab.inactive_background": "#00000000",
    "tab.active_background": "#3f3f4650",
    "border": "#00000000",
    "status_bar.background": "#00000000",
    "title_bar.background": "#00000000",
    "border.variant": "#00000000",
    "scrollbar.track.background": "#52525b20",
    "scrollbar.track.border": "#00000000",
    "scrollbar.thumb.background": "#52525b30",
    "scrollbar.thumb.border": "#00000000",
    "elevated_surface.background": "#00000090",
    "surface.background": "#00000090",
    "editor.active_line_number": "#ffffffcc",
    "editor.gutter.background": "#00000000",
    "editor.indent_guide": "#ffffff30",
    "editor.indent_guide_active": "#ffffff80",
    "editor.line_number": "#ffffff80",
    "editor.active_line.background": "#3f3f4640"
  }
}
