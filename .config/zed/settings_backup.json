// Zed settings
//
// For information on how to configure <PERSON><PERSON>, see the Zed
// documentation: https://zed.dev/docs/configuring-zed
//
// To see all of <PERSON>ed's default settings without changing your
// custom settings, run the `zed: Open Default Settings` command
// from the command palette
{
  "ssh_connections": [
    {
      "host": "**********",
      "username": "ubuntu",
      "args": [
        "-i",
        "~/Developers/ravenmail/ssh-keys/prod/monitoring-prod-ssh-key"
      ],
      "projects": [
        {
          "paths": [
            "/bin"
          ]
        },
        {
          "paths": [
            "/etc"
          ]
        },
        {
          "paths": [
            "~/go"
          ]
        },
        {
          "paths": [
            "~/nats-all-subjects-count"
          ]
        }
      ],
      "nickname": "monitoring"
    },
    {
      "host": "**********",
      "username": "ubuntu",
      "args": [
        "-i",
        "~/Developers/ravenmail/ssh-keys/prod/nats-prod-nodes-ssh-key"
      ],
      "projects": [
        {
          "paths": [
            "~/tmp.txt"
          ]
        }
      ]
    }
  ],
  "icon_theme": "Charmed Icons",
  "assistant": {
    "default_profile": "ask",
    "always_allow_tool_actions": false,
    "default_model": {
      "provider": "copilot_chat",
      "model": "gpt-4o"
    },
    "version": "2"
  },
  "features": {
    "inline_completion_provider": "none"
  },
  "vim_mode": true,
  "telemetry": {
    "diagnostics": false,
    "metrics": false
  },
  "ui_font_size": 16,
  "buffer_font_size": 15.0,
  "theme": {
    "mode": "system",
    "light": "One Light",
    "dark": "Catppuccin Mocha"
  },
  "remove_trailing_whitespace_on_save": false
}
